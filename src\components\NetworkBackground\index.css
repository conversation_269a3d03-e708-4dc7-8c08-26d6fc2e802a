/* Network Background Styles */
.network-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  opacity: 1;
}

.network-background canvas {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  filter: brightness(1.2) contrast(1.1);
}

/* Enhanced visibility for dark mode - more prominent */
.dark .network-background canvas {
  filter: brightness(1.6) contrast(1.5) saturate(1.4);
}

/* Light mode enhancement - more prominent */
.network-background canvas {
  filter: brightness(1.3) contrast(1.4) saturate(1.3);
}

/* Ensure content is above the background */
.content-overlay {
  position: relative;
  z-index: 10;
}

/* Reduced backdrop blur for more visibility */
.content-backdrop {
  backdrop-filter: blur(0.2px);
  background: rgba(255, 255, 255, 0.01);
}

.dark .content-backdrop {
  background: rgba(0, 0, 0, 0.01);
}

/* Animation for smooth theme transitions */
.network-background,
.content-backdrop {
  transition: all 0.3s ease-in-out;
}
