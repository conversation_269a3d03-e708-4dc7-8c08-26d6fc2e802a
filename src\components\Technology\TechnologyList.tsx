"use client";

import Image from "next/image";
import Marquee from "react-fast-marquee";
import { useEffect, useState } from "react";
import { useLocale } from "next-intl";
import { Technology } from "@/lib/types/types";
import {
  backgroundFirstColorDark,
  backgroundHoverFirstColorLight,
  backgroundHoverSecondColorDark,
  backgroundSecondColorLight,
} from "../Color";

export default function TechnologyList({ data }: { data: Technology[] }) {
  const locale = useLocale();
  const [mounted, setMounted] = useState(false);

  // Determine direction based on locale
  const isRTL = locale === 'ar';
  const direction = isRTL ? 'right' : 'left';

  useEffect(() => {
    setMounted(true);
  }, []);


  // Prevent hydration mismatch by showing a placeholder until mounted
  if (!mounted) {
    return (
      <div className="py-10 overflow-hidden">
        <div className="flex justify-center items-center space-x-10">
          {data.slice(0, 5).map((technology) => (
            <div
              key={technology.publicId}
              className={`flex-shrink-0 w-32 h-32 flex items-center justify-center rounded-xl ${backgroundFirstColorDark} ${backgroundHoverSecondColorDark} ${backgroundHoverFirstColorLight} ${backgroundSecondColorLight} transition-all duration-300 shadow-md`}
            >
              <Image
                src={technology.logo}
                alt={technology.name}
                width={80}
                height={80}
                className="object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="py-10 overflow-hidden">
      <Marquee
        pauseOnHover
        speed={100}
        gradient={false}
        direction={direction}
      >
        {data.map((technology) => (
          <div
            key={technology.publicId}
            className={`mx-10 flex-shrink-0 w-32 h-32 flex items-center justify-center rounded-xl ${backgroundFirstColorDark} ${backgroundHoverSecondColorDark} ${backgroundHoverFirstColorLight} ${backgroundSecondColorLight} transition-all duration-300 shadow-md`}
          >
            <Image
              src={technology.logo}
              alt={technology.name}
              width={80}
              height={80}
              className="object-contain"
            />
          </div>
        ))}
      </Marquee>
    </div>
  );
}
