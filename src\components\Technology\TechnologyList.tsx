"use client";

import Image from "next/image";
import Marquee from "react-fast-marquee";
import { useEffect, useState } from "react";
import { Technology } from "@/lib/types/types";
import {
  backgroundFirstColorDark,
  backgroundHoverFirstColorLight,
  backgroundHoverSecondColorDark,
  backgroundSecondColorLight,
} from "../Color";

export default function TechnologyList({ data }: { data: Technology[] }) {
  const [dir, setDir] = useState<"ltr" | "rtl">("ltr");

useEffect(() => {
  const htmlDir = document.documentElement.getAttribute("dir");
  setDir(htmlDir === "rtl" ? "rtl" : "ltr");

  // ✅ Observe changes
  const observer = new MutationObserver(() => {
    const newDir = document.documentElement.getAttribute("dir");
    setDir(newDir === "rtl" ? "rtl" : "ltr");
  });

  observer.observe(document.documentElement, { attributes: true, attributeFilter: ["dir"] });
  return () => observer.disconnect();
}, []);


  return (
    <div className="py-10 overflow-hidden">
      <Marquee
        pauseOnHover
        speed={100}
        gradient={false}
        direction={dir === "rtl" ? "right" : "left"} // ✅ auto direction
      >
        {data.map((technology) => (
          <div
            key={technology.publicId}
            className={`mx-10 flex-shrink-0 w-32 h-32 flex items-center justify-center rounded-xl ${backgroundFirstColorDark} ${backgroundHoverSecondColorDark} ${backgroundHoverFirstColorLight} ${backgroundSecondColorLight} transition-all duration-300 shadow-md`}
          >
            <Image
              src={technology.logo}
              alt={technology.name}
              width={80}
              height={80}
              className="object-contain"
            />
          </div>
        ))}
      </Marquee>
    </div>
  );
}
