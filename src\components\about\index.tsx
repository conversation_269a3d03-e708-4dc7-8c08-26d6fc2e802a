import React from "react";
import {getHome } from "../home/<USER>/getHome";
import { backgroundFirstColorDark, backgroundSecondColorLight, gradientColorFirstTextLight, gradientColorSeconedTextDark} from "../Color";

export default async function AboutComponent() {
  const { title, content, t } = await getHome();

  return (
    <section
      id="about"
      className={`py-20 transition-all duration-300 ${backgroundSecondColorLight} ${backgroundFirstColorDark} relative z-10    transition-all duration-300`}
    >
      <div className="container mx-auto px-6 ">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          <div className="w-full reveal">
            <h2 className="text-4xl md:text-5xl font-extrabold mb-6">
              {t("who")} <br />
              <span className={`${gradientColorSeconedTextDark } ${gradientColorFirstTextLight}`} >{title ?? t("subtitle")}</span>
            </h2>
            <p className="dark:text-gray-300 text-black mb-4 leading-relaxed text-xl">
              {content ?? t("description")}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
